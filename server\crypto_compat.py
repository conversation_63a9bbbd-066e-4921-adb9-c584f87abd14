"""
Compatibility layer for crypto operations using python3-cryptography
This provides PyCryptodome-compatible interface using the installed cryptography library
"""

try:
    # Try to use PyCryptodome first
    from Crypto.Cipher import AES
    from Crypto.PublicKey import RSA
    from Crypto.Cipher import PKCS1_OAEP
    from Crypto.Util.Padding import pad, unpad
    from Crypto.Random import get_random_bytes
    from Crypto.Hash import SHA256
    print("Using PyCryptodome")
    
except ImportError:
    print("PyCryptodome not available, using cryptography library compatibility layer")
    
    # Fallback to cryptography library with compatibility interface
    from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
    from cryptography.hazmat.primitives import padding, serialization, hashes
    from cryptography.hazmat.primitives.asymmetric import rsa, padding as asym_padding
    from cryptography.hazmat.backends import default_backend
    import os
    
    # Create PyCryptodome-like interface
    class AES:
        BLOCKSIZE = 16
        block_size = 16  # PyCryptodome compatibility
        MODE_CBC = 2  # Standard CBC mode constant
        
        @staticmethod
        def new(key, mode_constant, iv=None):
            return AESCompat(key, mode_constant, iv)
    
    class AESCompat:
        def __init__(self, key, mode_constant, iv=None):
            self.key = key
            self.iv = iv or os.urandom(16)
            
            if mode_constant == AES.MODE_CBC:
                # CBC mode
                cipher = Cipher(algorithms.AES(key), modes.CBC(self.iv), backend=default_backend())
                self.encryptor = cipher.encryptor()
                self.decryptor = cipher.decryptor()
            else:
                raise ValueError(f"Unsupported AES mode: {mode_constant}")
        
        def encrypt(self, data):
            # Add PKCS7 padding
            padder = padding.PKCS7(128).padder()
            padded_data = padder.update(data) + padder.finalize()
            return self.encryptor.update(padded_data) + self.encryptor.finalize()
        
        def decrypt(self, data):
            decrypted = self.decryptor.update(data) + self.decryptor.finalize()
            # Remove PKCS7 padding
            unpadder = padding.PKCS7(128).unpadder()
            return unpadder.update(decrypted) + unpadder.finalize()
    
    class RSA:
        @staticmethod
        def generate(bits, randfunc=None):
            # randfunc parameter is ignored when using cryptography library
            # as it has its own secure random number generation
            private_key = rsa.generate_private_key(
                public_exponent=65537,
                key_size=bits,
                backend=default_backend()
            )
            return RSAKeyCompat(private_key)
        
        @staticmethod
        def import_key(data):
            try:
                if isinstance(data, str):
                    data = data.encode()
                private_key = serialization.load_der_private_key(
                    data, password=None, backend=default_backend()
                )
                return RSAKeyCompat(private_key)
            except:
                # Try as public key
                public_key = serialization.load_der_public_key(
                    data, backend=default_backend()
                )
                return RSAKeyCompat(None, public_key)
    
    class RSAKeyCompat:
        def __init__(self, private_key=None, public_key=None):
            self.private_key = private_key
            self.public_key = public_key or (private_key.public_key() if private_key else None)
        
        def encrypt(self, data):
            if isinstance(data, str):
                data = data.encode()
            return self.public_key.encrypt(
                data,
                asym_padding.OAEP(
                    mgf=asym_padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )

        def decrypt(self, data):
            return self.private_key.decrypt(
                data,
                asym_padding.OAEP(
                    mgf=asym_padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )
        
        def export_key(self, format='DER'):
            if format == 'DER':
                return self.private_key.private_key_serialization(
                    encoding=serialization.Encoding.DER,
                    format=serialization.PrivateFormat.PKCS8,
                    encryption_algorithm=serialization.NoEncryption()
                )
            else:
                raise ValueError("Unsupported format")
    
    class PKCS1_OAEP:
        def __init__(self, key, hashAlgo=None):
            self.key = key
            self.hash_algo = hashAlgo  # Ignored in this implementation
        
        def encrypt(self, data):
            return self.key.encrypt(data)
        
        def decrypt(self, data):
            return self.key.decrypt(data)
        
        @staticmethod
        def new(key, hashAlgo=None):
            return PKCS1_OAEP(key, hashAlgo)
    
    def pad(data, block_size):
        padder = padding.PKCS7(block_size * 8).padder()
        return padder.update(data) + padder.finalize()
    
    def unpad(data, block_size):
        unpadder = padding.PKCS7(block_size * 8).unpadder()
        return unpadder.update(data) + unpadder.finalize()
    
    def get_random_bytes(length):
        return os.urandom(length)
    
    # Export SHA256 for PKCS1_OAEP compatibility
    class SHA256:
        """SHA256 hash algorithm class for PKCS1_OAEP compatibility"""
        @staticmethod
        def new(data=None):
            """Create a new SHA256 hash object"""
            import hashlib
            return hashlib.sha256(data) if data else hashlib.sha256()

        @staticmethod
        def digest_size():
            """Return the digest size for SHA256"""
            return 32
    
    # MODE_CBC constant is already set in the AES class definition

# Export SHA256 for PKCS1_OAEP compatibility (available for both PyCryptodome and cryptography)
if 'SHA256' not in globals():
    class SHA256:
        """SHA256 hash algorithm class for PKCS1_OAEP compatibility"""
        @staticmethod
        def new(data=None):
            """Create a new SHA256 hash object"""
            import hashlib
            return hashlib.sha256(data) if data else hashlib.sha256()

        @staticmethod
        def digest_size():
            """Return the digest size for SHA256"""
            return 32