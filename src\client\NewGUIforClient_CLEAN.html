<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CyberBackup Pro - v3.0 (Clean)</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Orbitron', monospace;
            background: #000000;
            color: #FFFFFF;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            width: 100%;
            background: rgba(26, 26, 46, 0.95);
            border: 2px solid #00FFFF;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 0 20px #00FFFF;
        }

        h1 {
            text-align: center;
            color: #00FFFF;
            text-shadow: 0 0 10px #00FFFF;
            margin-bottom: 30px;
            font-size: 2.5em;
        }

        .status {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid #333;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .status-label {
            color: #888;
        }

        .status-value {
            color: #00FF00;
            font-weight: bold;
        }

        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .cyber-btn {
            background: linear-gradient(45deg, #FF00FF, #00FFFF);
            border: none;
            border-radius: 5px;
            color: #000;
            font-weight: bold;
            padding: 15px 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
            min-width: 120px;
        }

        .cyber-btn:hover {
            box-shadow: 0 0 15px #FF00FF;
            transform: translateY(-2px);
        }

        .cyber-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .progress-section {
            margin-top: 20px;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #333;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00FF00, #00FFFF);
            width: 0%;
            transition: width 0.3s ease;
        }

        .logs {
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid #333;
            border-radius: 5px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .log-success { color: #00FF00; }
        .log-error { color: #FF0040; }
        .log-info { color: #00FFFF; }
        .log-warning { color: #FFFF00; }

        .debug-console {
            position: fixed;
            top: 10px;
            right: 10px;
            width: 300px;
            height: 150px;
            background: #000;
            color: #00FF00;
            border: 1px solid #00FF00;
            padding: 10px;
            font-family: monospace;
            font-size: 11px;
            overflow-y: auto;
            z-index: 9999;
        }

        .config-section {
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            color: #00FFFF;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input {
            width: 100%;
            padding: 10px;
            background: rgba(0, 0, 0, 0.7);
            border: 1px solid #333;
            border-radius: 3px;
            color: #FFF;
            font-size: 14px;
        }

        .form-group input:focus {
            border-color: #00FFFF;
            box-shadow: 0 0 5px #00FFFF;
            outline: none;
        }
    </style>
</head>
<body>
    <div class="debug-console" id="debugConsole">
        <div>Debug Console Ready</div>
    </div>

    <div class="container">
        <h1>🚀 CYBERBACKUP PRO v3.0</h1>
        
        <div class="status">
            <h3>System Status</h3>
            <div class="status-item">
                <span class="status-label">API Server:</span>
                <span class="status-value" id="apiStatus">Checking...</span>
            </div>
            <div class="status-item">
                <span class="status-label">Backup Server:</span>
                <span class="status-value" id="backupStatus">Checking...</span>
            </div>
            <div class="status-item">
                <span class="status-label">Connection:</span>
                <span class="status-value" id="connectionStatus">Disconnected</span>
            </div>
            <div class="status-item">
                <span class="status-label">Operation:</span>
                <span class="status-value" id="operationStatus">Ready</span>
            </div>
        </div>

        <div class="config-section">
            <h3>Server Configuration</h3>
            <div class="form-group">
                <label for="serverHost">Server Host:</label>
                <input type="text" id="serverHost" value="127.0.0.1">
            </div>
            <div class="form-group">
                <label for="serverPort">Server Port:</label>
                <input type="number" id="serverPort" value="1256">
            </div>
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" value="testuser">
            </div>
        </div>

        <div class="controls">
            <button class="cyber-btn" id="connectBtn" onclick="connectToServer()">CONNECT</button>
            <button class="cyber-btn" id="startBtn" onclick="startBackup()" disabled>START BACKUP</button>
            <button class="cyber-btn" id="stopBtn" onclick="stopBackup()" disabled>STOP</button>
            <button class="cyber-btn" id="testBtn" onclick="testAPI()">TEST API</button>
        </div>

        <div class="progress-section">
            <h3>Progress</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div id="progressText">0% - Ready</div>
        </div>

        <div class="logs" id="logArea">
            <div class="log-entry log-info">[SYSTEM] CyberBackup Pro v3.0 initialized</div>
        </div>
    </div>

    <script>
        // Global debug function
        function debugLog(message) {
            console.log('[DEBUG] ' + message);
            const debugDiv = document.getElementById('debugConsole');
            const now = new Date().toLocaleTimeString();
            debugDiv.innerHTML += '<div>[' + now + '] ' + message + '</div>';
            debugDiv.scrollTop = debugDiv.scrollHeight;
        }

        // Global state
        let appState = {
            connected: false,
            backing_up: false,
            progress: 0,
            apiUrl: 'http://localhost:9090'
        };

        // Initialize on page load
        debugLog('Script starting...');
        debugLog('DOM state: ' + document.readyState);

        // API functions
        async function apiCall(endpoint, options = {}) {
            try {
                debugLog('Making API call: ' + endpoint);
                const response = await fetch(appState.apiUrl + endpoint, options);
                
                if (!response.ok) {
                    throw new Error('HTTP ' + response.status + ': ' + response.statusText);
                }
                
                const data = await response.json();
                debugLog('API response: ' + JSON.stringify(data));
                return data;
            } catch (error) {
                debugLog('API error: ' + error.message);
                addLog('API Error: ' + error.message, 'error');
                throw error;
            }
        }

        async function testAPI() {
            debugLog('Testing API connection...');
            addLog('Testing API connection...', 'info');
            
            try {
                const health = await apiCall('/health');
                addLog('✅ API Server: ' + health.status, 'success');
                document.getElementById('apiStatus').textContent = 'Online';
                
                const status = await apiCall('/api/status');
                addLog('✅ Status check successful', 'success');
                document.getElementById('backupStatus').textContent = status.connected ? 'Online' : 'Offline';
                
            } catch (error) {
                addLog('❌ API test failed: ' + error.message, 'error');
                document.getElementById('apiStatus').textContent = 'Offline';
            }
        }

        async function connectToServer() {
            debugLog('Connecting to server...');
            addLog('Connecting to backup server...', 'info');
            
            try {
                const config = {
                    host: document.getElementById('serverHost').value,
                    port: parseInt(document.getElementById('serverPort').value),
                    username: document.getElementById('username').value
                };
                
                const result = await apiCall('/api/connect', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(config)
                });
                
                if (result.success) {
                    appState.connected = true;
                    addLog('✅ Connected to server', 'success');
                    document.getElementById('connectionStatus').textContent = 'Connected';
                    document.getElementById('connectBtn').disabled = true;
                    document.getElementById('startBtn').disabled = false;
                } else {
                    addLog('❌ Connection failed: ' + result.message, 'error');
                }
                
            } catch (error) {
                addLog('❌ Connection error: ' + error.message, 'error');
            }
        }

        async function startBackup() {
            debugLog('Starting backup...');
            addLog('Starting backup operation...', 'info');
            
            try {
                // For now, create a test file
                const fileInfo = {
                    name: 'test_backup.txt',
                    path: 'test_file_path',
                    size: 1024
                };
                
                const result = await apiCall('/api/start_backup', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(fileInfo)
                });
                
                if (result.success) {
                    appState.backing_up = true;
                    addLog('✅ Backup started', 'success');
                    document.getElementById('operationStatus').textContent = 'Backing up';
                    document.getElementById('startBtn').disabled = true;
                    document.getElementById('stopBtn').disabled = false;
                    
                    // Start monitoring progress
                    monitorProgress();
                } else {
                    addLog('❌ Backup failed to start: ' + result.message, 'error');
                }
                
            } catch (error) {
                addLog('❌ Backup error: ' + error.message, 'error');
            }
        }

        async function stopBackup() {
            debugLog('Stopping backup...');
            addLog('Stopping backup...', 'warning');
            
            try {
                const result = await apiCall('/api/stop', { method: 'POST' });
                
                if (result.success) {
                    appState.backing_up = false;
                    addLog('✅ Backup stopped', 'success');
                    document.getElementById('operationStatus').textContent = 'Stopped';
                    document.getElementById('startBtn').disabled = false;
                    document.getElementById('stopBtn').disabled = true;
                } else {
                    addLog('❌ Stop failed: ' + result.message, 'error');
                }
                
            } catch (error) {
                addLog('❌ Stop error: ' + error.message, 'error');
            }
        }

        async function monitorProgress() {
            if (!appState.backing_up) return;
            
            try {
                const status = await apiCall('/api/status');
                
                // Update progress
                appState.progress = status.progress || 0;
                document.getElementById('progressFill').style.width = appState.progress + '%';
                document.getElementById('progressText').textContent = appState.progress + '% - ' + status.message;
                
                // Check if completed
                if (status.status === 'completed') {
                    appState.backing_up = false;
                    addLog('✅ Backup completed successfully!', 'success');
                    document.getElementById('operationStatus').textContent = 'Completed';
                    document.getElementById('startBtn').disabled = false;
                    document.getElementById('stopBtn').disabled = true;
                    return;
                }
                
                if (status.status === 'failed') {
                    appState.backing_up = false;
                    addLog('❌ Backup failed: ' + status.message, 'error');
                    document.getElementById('operationStatus').textContent = 'Failed';
                    document.getElementById('startBtn').disabled = false;
                    document.getElementById('stopBtn').disabled = true;
                    return;
                }
                  // Continue monitoring - reduced frequency
                setTimeout(monitorProgress, 3000);
                
            } catch (error) {
                debugLog('Progress monitoring error: ' + error.message);
                setTimeout(monitorProgress, 5000); // Retry with longer delay
            }
        }

        function addLog(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry log-' + type;
            logEntry.textContent = '[' + timestamp + '] ' + message;
            logArea.appendChild(logEntry);
            logArea.scrollTop = logArea.scrollHeight;
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            debugLog('DOM loaded, initializing...');
            addLog('System initialized - Ready for operations', 'success');
            
            // Test API connection on startup
            setTimeout(testAPI, 1000);
        });

        debugLog('Script loaded successfully');
        
    </script>
</body>
</html>
